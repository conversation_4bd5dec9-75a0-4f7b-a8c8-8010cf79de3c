"use client";

import React from 'react';
import { GeneratedFile } from '../types';
import FileViewer from './file-viewer';
import J<PERSON><PERSON><PERSON> from 'jszip';
import { saveAs } from 'file-saver';
import ExportButton from './export-button';

interface ContentViewerPanelProps {
  files: GeneratedFile[];
  isGenerating: boolean;
  onViewModeChange: (fileId: string, viewMode: 'code' | 'preview' | 'split') => void;
  onVersionChange?: (fileId: string, versionIndex: number) => void;
  // 流式相关属性
  streamingContent?: string;
  isStreaming?: boolean;
}

const ContentViewerPanel: React.FC<ContentViewerPanelProps> = ({
  files,
  isGenerating,
  onViewModeChange,
  onVersionChange,
  streamingContent,
  isStreaming
}) => {
  // ========== 文件去重与详细日志 ===========
  // 日志：原始文件列表
  console.log('[ContentViewerPanel] 原始 files:', files.map(f => ({ id: f.id, name: f.name, versions: f.versions?.length || 0, currentVersionIndex: f.currentVersionIndex })));

  // 使用 Map 以 name+contentType 去重，并合并所有同名同类型文件的版本
  // 使用 Map 以 name+contentType 为 key 合并同名文件
  const uniqueFilesMap = React.useMemo(() => {
    const filesMap = new Map<string, GeneratedFile>();

    // 第一遍遍历：收集所有版本并按时间戳排序
    const versionsMap = new Map<string, Array<{
      content: string;
      timestamp: number;
      taskNumber?: number;
      taskDescription?: string;
    }>>();

    // 收集所有版本
    files.forEach(file => {
      const key = `${file.name}__${file.contentType}`;
      let existingVersions = versionsMap.get(key) || [];

      // 添加当前文件的所有版本
      if (file.versions && Array.isArray(file.versions)) {
        file.versions.forEach(version => {
          if (!existingVersions.find(v => v.content === version.content)) {
            existingVersions.push(version);
          }
        });
      }

      // 如果没有版本历史，将当前内容作为一个版本
      if ((!file.versions || file.versions.length === 0) && file.content) {
        if (!existingVersions.find(v => v.content === file.content)) {
          existingVersions.push({
            content: file.content,
            timestamp: file.timestamp,
            taskDescription: '初始版本'
          });
        }
      }

      // 按时间戳排序
      existingVersions.sort((a, b) => (a.timestamp || 0) - (b.timestamp || 0));
      versionsMap.set(key, existingVersions);
    });

    // 第二遍遍历：构建合并后的文件对象
    files.forEach(file => {
      const key = `${file.name}__${file.contentType}`;

      if (!filesMap.has(key)) {
        const allVersions = versionsMap.get(key) || [];

        // 找到同名文件中最新的那个（基于时间戳）
        const sameNameFiles = files.filter(f => f.name === file.name && f.contentType === file.contentType);
        const latestFile = sameNameFiles.reduce((latest, current) =>
          current.timestamp > latest.timestamp ? current : latest
        );

        // 使用最新文件的版本索引，如果没有则使用最后一个版本
        const currentIndex = latestFile.currentVersionIndex ?? Math.max(0, allVersions.length - 1);

        console.log('[ContentViewerPanel] 合并同名文件:', {
          key,
          filesCount: sameNameFiles.length,
          latestFileId: latestFile.id,
          latestFileTimestamp: latestFile.timestamp,
          currentVersionIndex: latestFile.currentVersionIndex,
          calculatedIndex: currentIndex,
          versionsCount: allVersions.length
        });

        // 创建合并后的文件对象
        const mergedFile: GeneratedFile = {
          ...latestFile, // 使用最新文件的属性
          versions: allVersions,
          currentVersionIndex: currentIndex,
          content: allVersions[currentIndex]?.content || latestFile.content,
          isModified: true
        };

        filesMap.set(key, mergedFile);
      }
    });

    return filesMap;
  }, [files]);

  // 从映射中提取唯一文件列表
  const uniqueFiles = React.useMemo(() => {
    const filesList = Array.from(uniqueFilesMap.values());
    console.log('[ContentViewerPanel] 重新计算 uniqueFiles:', filesList.map(f => ({
      id: f.id,
      name: f.name,
      versions: f.versions?.length || 0,
      currentVersionIndex: f.currentVersionIndex,
      isModified: f.isModified
    })));
    return filesList;
  }, [uniqueFilesMap]);

  // 日志：去重后文件列表
  console.log('[ContentViewerPanel] 去重后 uniqueFiles:', uniqueFiles.map(f => ({ id: f.id, name: f.name, versions: f.versions?.length || 0, currentVersionIndex: f.currentVersionIndex })));

  // 日志：每个文件的版本内容摘要
  uniqueFiles.forEach(f => {
    if (f.versions && Array.isArray(f.versions)) {
      f.versions.forEach((v, idx) => {
        console.log(`[ContentViewerPanel] 文件 ${f.id} 版本${idx}: 内容长度=${v.content?.length || 0}`);
      });
    }
  });

  // 日志：去重后文件列表
  console.log('[ContentViewerPanel] 去重后 uniqueFiles:', uniqueFiles.map(f => ({ id: f.id, name: f.name, versions: f.versions?.length || 0, currentVersionIndex: f.currentVersionIndex })));

  // 日志：每个文件的版本内容摘要
  uniqueFiles.forEach(f => {
    if (f.versions && Array.isArray(f.versions)) {
      f.versions.forEach((v, idx) => {
        console.log(`[ContentViewerPanel] 文件 ${f.id} 版本${idx}: 内容长度=${v.content?.length || 0}`);
      });
    }
  });

  // --- 后续逻辑直接使用 uniqueFiles ---
  // 处理下载所有文件
  const handleDownloadAll = async () => {
    // 使用合并后的文件列表，只下载已完成的文件
    const completedFiles = uniqueFiles.filter(file => file.status === 'completed');

    if (completedFiles.length === 0) {
      alert('没有可下载的文件');
      return;
    }

    // 如果只有一个文件，直接下载
    if (completedFiles.length === 1) {
      const file = completedFiles[0];
      // 使用最新版本的内容
      const content = file.versions?.[file.versions.length - 1]?.content || file.content;
      const blob = new Blob([content], { type: file.contentType === 'html' ? 'text/html' : 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.name;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      return;
    }

    try {
      // 如果有多个文件，创建一个zip文件
      const zip = new JSZip();

      // 添加所有文件到zip
      completedFiles.forEach(file => {
        // 使用最新版本的内容
        const content = file.versions?.[file.versions.length - 1]?.content || file.content;
        zip.file(file.name, content);
        console.log(`添加文件到ZIP: ${file.name}`, {
          contentLength: content.length,
          versions: file.versions?.length || 0
        });
      });

      // 生成zip文件
      const zipBlob = await zip.generateAsync({ type: 'blob' });

      // 下载zip文件
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      saveAs(zipBlob, `generated-files-${timestamp}.zip`);

      console.log(`已打包下载 ${completedFiles.length} 个文件`);
    } catch (error) {
      console.error('创建ZIP文件时出错:', error);
      alert('下载文件时出错，请查看控制台获取详细信息');
    }
  };

// [已清理死代码和无主代码块，所有逻辑只用顶部 uniqueFiles]

  return (
    <div className="flex-1 flex flex-col bg-gradient-to-br from-white to-gray-50 shadow-sm h-full overflow-hidden">
      {/* 顶部工具栏 */}
      <div className="flex-none bg-gradient-to-r from-blue-50 to-gray-50 border-b border-gray-200 p-4 flex justify-between items-center">
        <h2 className="font-semibold text-gray-800 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          生成的文件
          {files.length > 0 && (
            <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
              {files.length}
            </span>
          )}
        </h2>
        {files.length > 0 && (
          <div className="flex items-center gap-2">
            <button
              onClick={handleDownloadAll}
              className="px-3 py-1.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:shadow-md transition-all duration-200 text-sm flex items-center"
              disabled={isGenerating || files.every(file => file.status !== 'completed')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
              下载全部文件
            </button>
            <ExportButton
              files={uniqueFiles}
              disabled={isGenerating || files.every(file => file.status !== 'completed')}
            />
          </div>
        )}
      </div>

      {/* 文件内容区域 - 可滚动 */}
      <div className="flex-1 overflow-y-auto p-4 h-full">
        {files.length === 0 && !isStreaming ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center bg-white p-8 rounded-xl shadow-sm border border-gray-100 max-w-md">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-700 mb-2">还没有生成的内容</h3>
              <p className="text-gray-500 mb-4">在左侧输入您的需求，AI将为您生成内容</p>
              <div className="bg-blue-50 p-3 rounded-lg border border-blue-100 text-sm text-blue-700">
                <p className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1.5 flex-shrink-0 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>生成的内容将自动显示在此区域，您可以查看、编辑和下载文件。</span>
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6">
            {/* 已有文件列表 */}
            {uniqueFiles.map((file) => (
              <FileViewer
                key={file.id}
                file={file}
                onViewModeChange={(viewMode) => onViewModeChange(file.id, viewMode)}
                onVersionChange={onVersionChange ? (versionIndex) => {
                  console.log('[ContentViewerPanel] 调用版本变更:', {
                    fileId: file.id,
                    fileName: file.name,
                    currentVersionIndex: file.currentVersionIndex,
                    newVersionIndex: versionIndex,
                    versionsCount: file.versions?.length || 0
                  });

                  // 当编辑内容并保存为新版本时，需要更新文件对象
                  if (file.versions && versionIndex === file.versions.length - 1) {
                    // 更新文件对象的版本属性
                    const updatedFile = {
                      ...file,
                      currentVersionIndex: versionIndex,
                      isModified: true
                    };

                    // 在文件映射中更新文件
                    uniqueFilesMap.set(`${file.name}__${file.contentType}`, updatedFile);
                  }

                  // 调用父组件的版本变更回调
                  onVersionChange(file.id, versionIndex);
                } : undefined}
                contentClassName={(file.content && (file.content.includes('<div class="slide"') || file.content.includes('class="slide')))
                  ? "w-full max-w-[1280px] mx-auto overflow-auto"
                  : "aspect-video w-full max-w-[1280px] mx-auto overflow-auto"}
              />
            ))}

            {/* 流式内容预览 - 显示在已有文件之后 */}
            {isStreaming && streamingContent && (
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <div className="animate-pulse h-2 w-2 rounded-full bg-blue-500 mr-2"></div>
                  <h3 className="text-sm font-medium text-blue-800">正在生成内容...</h3>
                </div>
                <div
                  ref={(el) => {
                    if (el) {
                      // 自动滚动到底部
                      el.scrollTop = el.scrollHeight;
                    }
                  }}
                  className="bg-white rounded-md border border-blue-200 p-4 max-h-96 overflow-y-auto"
                >
                  <div className="text-sm text-gray-700 whitespace-pre-wrap">
                    {streamingContent}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentViewerPanel;
